/*
 * @Author: 老范
 * @Date: 2023-07-21 14:57:39
 * @LastEditors: 老范
 * @LastEditTime: 2023-08-01 10:52:52
 * @Description: 请填写简介
 */

export interface traceType {
  x: Array<string | number>;
  y: Array<string | number>;
  name: string;
  type: string;
  text?: Array<string | number>;
  hovertemplate?: string;
  orientation?: string;
  base?: Array<string | number>;
}
export interface layoutType {
  title: string;
  width?: number;
  height?: number;
  autosize?: boolean;
  hovermode?: string;
  margin?: Object;
  yaxis?: Object;
  xaxis?: Object;
}
