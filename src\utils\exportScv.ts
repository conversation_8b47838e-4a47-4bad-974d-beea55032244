/*
 * @Author: 老范
 * @Date: 2023-08-28 15:15:24
 * @LastEditors: 老范
 * @LastEditTime: 2023-08-28 16:38:17
 * @Description: 组织并导出csv文件
 */

import {sortResult} from '@/utils/sortString';

/**
 * @description: 组织并导出csv文件
 * @param {any} csvJsonAry 数据数组
 * @param {*} header csv数据表头
 * @return {*}
 */
export const downloadCsv = (csvJsonAry: any[], header: {}) => {
  let csvString = '';
  // 排序
  csvJsonAry = sortResult(csvJsonAry, 'letter');
  // 添加表头
  csvJsonAry.unshift(header);
  console.log('🚀 ~ file: exportScv.ts:19 ~ downloadCsv ~ csvJsonAry:', csvJsonAry);
  // 处理成json字符串
  csvJsonAry.forEach(item => {
    Object.keys(header).map(key => {
      const val = item[key];
      csvString += val + ',';
    });
    csvString += '\r\n';
  });
  csvString = 'data:application/csv;charset=utf-8,\ufeff' + encodeURIComponent(csvString);
  console.log(csvString);
};
