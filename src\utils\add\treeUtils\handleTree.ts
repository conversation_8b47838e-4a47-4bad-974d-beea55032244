/*
 * @Author: mxy
 * @Date: 2023-07-31 11:06:46
 * @LastEditors: 老范
 * @LastEditTime: 2023-08-28 13:55:19
 * @Description: 处理树节点相关方法
 */

import {treeDataType} from '@/views/text/data';

/**
 * @description 可以对字符串的没个字符进行换行
 * @param {string} name 需要对每个字符换行的字符串
 * @returns {string} str 换行处理后的字符串
 */
export function nameAddn(name: string): String {
  let str: String = '';
  for (let i = 0; i < name.length; i++) {
    str += name[i] + '\n';
  }
  return str;
}
/**
 *
 * @description 递归查找树节点
 * @param {Array<Object>} data 需要查找节点所在的树节点数组
 * @param ids 需要查找的节点的id
 * @param result 存储查找的结果
 * @returns 查找的结果
 */
export function recursionTree(data: Array<Object>, ids: string | number, result: Array<Object> = []) {
  data.forEach((res: any) => {
    if (res.id === ids) {
      result.push(res);
      return;
    } else {
      if (res.children) {
        recursionTree(res.children, ids, result);
      }
    }
  });
  return result;
}
/**
 * @description 获取树结构的最大层级 层级从0开始
 * @param {object[]} treeData 要获取最大层级的树结构数组
 * @returns 该树的最大层级
 */
export function getMaxlevel(treeData: any) {
  let maxLevel = 0;
  function loop(data: any, level: number) {
    data.forEach((item: any) => {
      item.level = level;
      if (level > maxLevel) {
        maxLevel = level;
      }
      if ('children' in item) {
        if (item.children && item.children.length > 0) {
          loop(item.children, (level += 1));
        }
      }
    });
  }
  loop(treeData, 0);
  return maxLevel;
}
/**
 * @description 将树形结构扁平化
 * @param {object[]} treeData 需要扁平化的树结构
 * @param {object[]} arr 扁平化后储存结果的数组
 * @returns 扁平化后的结果
 */
export function carryOutTreeData(treeData: object[], arr: {}[] = []) {
  treeData.forEach((res: any) => {
    arr.push(res);
    if (res.children) {
      carryOutTreeData(res.children, arr);
    }
  });
  return arr;
}

/**
 * @description: 递归给树节点添加id值
 * @param treeDom 树组件
 */
export function treeAddId(treeDom: treeDataType[]): {}[] {
  let ids = 0;
  function treeAddIds(treeDom: {id?: number; children: []; name: string}[]): {}[] {
    treeDom.forEach(res => {
      res['id'] = ids;
      ids += 1;
      if (res.children) {
        treeAddIds(res.children);
      }
    });
    return treeDom;
  }
  return treeAddIds(treeDom);
}
