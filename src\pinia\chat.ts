/*
 * @Author: AI Assistant
 * @Date: 2024-12-19
 * @Description: 聊天状态管理
 */

import {defineStore} from 'pinia';
import {ref, reactive} from 'vue';

// 消息类型定义
export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'thinking' | 'tool' | 'error';
  content: string;
  timestamp: Date;
  metadata?: any;
}

// 事件类型常量
export const EVENT_TYPES = {
  START: 'start',
  THINKING: 'thinking',
  CONTENT: 'content',
  TOOL_CALL: 'tool_call',
  TOOL_RESULT: 'tool_result',
  ERROR: 'error',
  FINAL: 'final',
} as const;

export const useChatStore = defineStore('chat', () => {
  // 状态
  const messages = ref<ChatMessage[]>([]);
  const isConnected = ref(false);
  const isProcessing = ref(false);
  const isThinking = ref(false);
  const thinkingText = ref('正在思考...');
  const currentAssistantMessage = ref<ChatMessage | null>(null);
  const currentContent = ref('');

  // 配置
  const config = reactive({
    // apiUrl: "http://*************:8000/chat",
    apiUrl: `${configUrl.controlerUrl}`,
    maxRetries: 3,
    retryDelay: 1000,
  });

  // 添加消息
  const addMessage = (message: ChatMessage) => {
    messages.value.push(message);
  };

  // 更新最后一条助手消息
  const updateLastAssistantMessage = (content: string) => {
    if (currentAssistantMessage.value) {
      currentAssistantMessage.value.content = content;
    }
  };

  // 创建新的助手消息
  const createAssistantMessage = () => {
    const message: ChatMessage = {
      id: Date.now().toString(),
      type: 'assistant',
      content: '',
      timestamp: new Date(),
    };
    currentAssistantMessage.value = message;
    addMessage(message);
    return message;
  };

  // 开始思考状态
  const startThinking = (text = '正在思考...') => {
    isThinking.value = true;
    thinkingText.value = text;
  };

  // 停止思考状态
  const stopThinking = () => {
    isThinking.value = false;
    thinkingText.value = '正在思考...';
  };

  // 处理流式内容
  const handleContent = (data: string) => {
    // 如果还没有当前助手消息，创建一个
    if (!currentAssistantMessage.value) {
      createAssistantMessage();
      stopThinking();
    }

    currentContent.value += data;
    if (currentAssistantMessage.value) {
      currentAssistantMessage.value.content = currentContent.value;
    }
  };

  // 处理工具调用
  const handleToolCall = (data: any) => {
    startThinking('🔧 正在调用工具...');
  };

  // 处理工具结果
  const handleToolResult = (data: any) => {
    startThinking('✅ 处理工具结果...');
  };

  // 处理错误
  const handleError = (error: string) => {
    const errorMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'error',
      content: `❌ 错误: ${error}`,
      timestamp: new Date(),
    };
    addMessage(errorMessage);
    stopThinking();
    isProcessing.value = false;
  };

  // 完成处理
  const finishProcessing = () => {
    stopThinking();
    currentAssistantMessage.value = null;
    currentContent.value = '';
    isProcessing.value = false;
  };

  // 发送消息到后端
  const sendMessage = async (message: string) => {
    if (isProcessing.value) return;

    isProcessing.value = true;
    startThinking('开始处理...');

    try {
      const response = await fetch(config.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({query: message}),
      });

      if (!response.ok) {
        throw new Error(`HTTP错误: 状态码 ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法读取响应流');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      const processStream = async ({done, value}: ReadableStreamReadResult<Uint8Array>) => {
        if (done) return;

        buffer += decoder.decode(value, {stream: true});
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (!line.trim() || !line.startsWith('data: ')) continue;

          try {
            const event = JSON.parse(line.slice(5));
            await handleStreamEvent(event);
          } catch (e) {
            console.error('解析SSE事件失败:', e, '原始数据:', line);
          }
        }

        const result = await reader.read();
        await processStream(result);
      };

      const result = await reader.read();
      await processStream(result);
    } catch (error) {
      console.error('发送消息失败:', error);
      handleError(error instanceof Error ? error.message : '未知错误');
    }
  };

  // 处理流事件
  const handleStreamEvent = async (event: any) => {
    switch (event.type) {
      case EVENT_TYPES.START:
        isProcessing.value = true;
        startThinking('开始处理...');
        break;

      case EVENT_TYPES.THINKING:
        startThinking(event.data);
        break;

      case EVENT_TYPES.CONTENT:
        handleContent(event.data);
        break;

      case EVENT_TYPES.TOOL_CALL:
        handleToolCall(event.data);
        break;

      case EVENT_TYPES.TOOL_RESULT:
        handleToolResult(event.data);
        break;

      case EVENT_TYPES.ERROR:
        handleError(event.data);
        break;

      case EVENT_TYPES.FINAL:
        finishProcessing();
        break;

      default:
        console.warn('未知事件类型:', event.type);
    }
  };

  // 初始化连接
  const initConnection = () => {
    isConnected.value = true;
    // 这里可以添加 WebSocket 连接逻辑
  };

  // 清空消息
  const clearMessages = () => {
    messages.value = [];
    currentAssistantMessage.value = null;
    currentContent.value = '';
    stopThinking();
    isProcessing.value = false;
  };

  return {
    // 状态
    messages,
    isConnected,
    isProcessing,
    isThinking,
    thinkingText,
    config,

    // 方法
    addMessage,
    sendMessage,
    initConnection,
    clearMessages,
    handleContent,
    handleToolCall,
    handleToolResult,
    handleError,
    finishProcessing,
    stopThinking,
  };
});
