/*
 * <AUTHOR> laofan
 * @Date         : 2023-03-06 14:17:30
 * @LastEditors: 老范
 * @LastEditTime: 2023-05-11 17:15:18
 * @Description  : 请填写简介
 */
import {defineStore} from 'pinia';
export interface controlerStatusType {
  statusList: Object;
  testStatus: boolean;
  testNumber: number;
}
const controlerStatus: controlerStatusType = {
  statusList: {
    // 未初始化
    uninitialized: {
      isInit: true,
      isStart: false,
      isPause: false,
      isContinue: false,
      isStop: false,
      isReset: true,
    },
    // 已初始化
    initialized: {
      isInit: false,
      isStart: true,
      isPause: false,
      isContinue: false,
      isStop: false,
      isReset: true,
    },
    // 已经开始
    running: {
      isInit: false,
      isStart: false,
      isPause: true,
      isContinue: false,
      isStop: true,
      isReset: false,
    },
    // 已暂停状态
    paused: {
      isInit: false,
      isStart: false,
      isPause: false,
      isContinue: true,
      isStop: true,
      isReset: true,
    },
  },
  testStatus: false,
  testNumber: 0,
};

export const useControler = defineStore('controler', {
  state: () => {
    return controlerStatus;
  },
});
