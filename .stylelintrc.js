module.exports = {
  plugins: ['stylelint-order'],
  // stylelint-config-recommended-less  用来检测less文件
  // stylelint-config-recommended-vue 用来检测vue文件
  // 注:当vue文件中 style使用less写的,那么就需要用postcss-less来解析less语句
  extends: [
    'stylelint-config-standard',
    'stylelint-config-recommended-vue', //解析vue文件
    'stylelint-config-recommended-less', //解析less文件
    'stylelint-config-html/html', //解析html文件
  ],
  rules: {
    /* 颜色系列 */
    'color-hex-case': [
      'lower',
      {
        message: 'Lowercase letters are easier to distinguish from numbers',
      },
    ], // 十六进制大小写 能简写就简写 例如 :#001122-#012
    'selector-pseudo-class-no-unknown': [
      true,
      {
        ignorePseudoClasses: ['deep'],
      },
    ],
    'string-quotes': null,
    'color-no-invalid-hex': true, // 禁止无效的十六进制颜色
    'selector-type-no-unknown': true,
    'string-no-newline': true, //不允许字符串（非转义）换行
    'selector-class-pattern': null, // 忽略类名命名问题
    'at-rule-empty-line-before': null,
    'at-rule-no-unknown': null,
    'at-rule-name-case': 'lower', // 指定@规则名的大小写
    'length-zero-no-unit': true, // 禁止零长度的单位（可自动修复）
    'shorthand-property-no-redundant-values': true, // 简写属性
    'number-leading-zero': 'always', //要求小于1的小数以0开头
    'declaration-block-no-duplicate-properties': true, // 禁止声明快重复属性
    'no-descending-specificity': true, // 禁止在具有较高优先级的选择器后出现被其覆盖的较低优先级的选择器
    'max-nesting-depth': 3, //嵌套的最大层级
    indentation: [
      2,
      {
        // 指定缩进  warning 提醒
        severity: 'warning',
      },
    ],
    'order/properties-order': [
      'position',
      'top',
      'right',
      'bottom',
      'left',
      'z-index',
      'display',
      'float',
      'width',
      'height',
      'max-width',
      'max-height',
      'min-width',
      'min-height',
      'padding',
      'padding-top',
      'padding-right',
      'padding-bottom',
      'padding-left',
      'margin',
      'margin-top',
      'margin-right',
      'margin-bottom',
      'margin-left',
      'margin-collapse',
      'margin-top-collapse',
      'margin-right-collapse',
      'margin-bottom-collapse',
      'margin-left-collapse',
      'overflow',
      'overflow-x',
      'overflow-y',
      'clip',
      'clear',
      'font',
      'font-family',
      'font-size',
      'font-smoothing',
      'osx-font-smoothing',
      'font-style',
      'font-weight',
      'line-height',
      'letter-spacing',
      'word-spacing',
      'color',
      'text-align',
      'text-decoration',
      'text-indent',
      'text-overflow',
      'text-rendering',
      'text-size-adjust',
      'text-shadow',
      'text-transform',
      'word-break',
      'word-wrap',
      'white-space',
      'vertical-align',
      'list-style',
      'list-style-type',
      'list-style-position',
      'list-style-image',
      'pointer-events',
      'background',
      'background-color',
      'border',
      'border-radius',
      'content',
      'outline',
      'outline-offset',
      'opacity',
      'filter',
      'visibility',
      'size',
      'cursor',
      'transform',
    ],
  },
  overrides: [
    {
      files: ['**/*.less'],
      customSyntax: 'postcss-less',
    },
  ],
};
// # rules优先级大于extends

