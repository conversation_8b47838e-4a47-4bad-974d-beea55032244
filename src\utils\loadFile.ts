/*
 * @Author: 老范
 * @Date: 2023-06-12 08:55:14
 * @LastEditors: 老范
 * @LastEditTime: 2024-03-07 10:27:43
 * @Description: 读取本地文件
 */

import {GeoJsonDataType, GeoJsonType, featureType} from '@/types';

/**
 * @description: 从nginx加载文件
 * @param {string} 文件地址
 * @param {void} 处理文件的回调函数,该回调的参数即为获取到的文件
 * @return {*}  无返回值
 */
export const loadFileFromNginx = (file: string, callback: (files: string) => void): void => {
  const rawFile = new XMLHttpRequest();
  rawFile.overrideMimeType('application/json');
  rawFile.open('GET', file, true);
  rawFile.onreadystatechange = function () {
    if (rawFile.readyState === 4 && rawFile.status === 200) {
      callback(rawFile.responseText);
    }
  };
  rawFile.send(null);
};
/**
 * @description: 加载本地的xml
 * @param {string} file，文件路径
 * @param {function} callback
 * @return {*}
 */
export const loadXmlFromNginx = (file: string) => {
  const rawFile = new XMLHttpRequest();
  rawFile.open('GET', file, true);
  rawFile.setRequestHeader('Content-Type', 'text/xml');
  rawFile.onload = function () {
    if (rawFile.readyState === 4 && rawFile.status === 200) {
      coordinateXml2obj(rawFile.responseText);
    }
  };
  rawFile.send(null);
};
const stationGeojson: GeoJsonDataType = {
  type: 'FeatureCollection',
  features: [],
};
const coordinateXml2obj = (xmlString: any) => {
  const arr: {}[] = [];
  //传入xml
  const xmlDoc = loadXML(xmlString);
  const TypeArr = xmlDoc!.getElementsByTagName('node');
  Array.from(TypeArr).forEach(item => {
    //obj存储每个数据类型
    if (item.children.length > 0) {
      const obj: any = {};
      const geojsonObj: featureType = {
        type: 'Feature',
        properties: {},
        geometry: {
          coordinates: [],
          type: 'Point',
        },
      };
      Array.from(item.children).forEach(tagItem => {
        if (tagItem.getAttribute('k') == 'name:zh') {
          obj.name = tagItem.getAttribute('v');
          geojsonObj.properties.name = obj.name;
          obj.coordinate = ` ${Number(item.getAttribute('lat'))},${Number(item.getAttribute('lon'))}`;
          // obj.coordinate = [Number(item.getAttribute('lon')), Number(item.getAttribute('lat'))];
          geojsonObj.geometry.coordinates = obj.coordinate;
        }
        if (tagItem.getAttribute('v') == 'stop') {
          obj.railway = tagItem.getAttribute('v');
          // geojsonObj.properties.railway = obj.railway;
        }
      });
      if (obj.railway && obj.name) {
        arr.push(obj);
        stationGeojson.features.push(geojsonObj);
      }
    }
  });
  // return stationGeojson;
  console.log('🚀 ~ coordinateXml2obj ~ arr:', arr);
  return arr;
};
function loadXML(xmlString: string) {
  let xmlDoc = null;
  try {
    const domParser = new DOMParser();
    xmlDoc = domParser.parseFromString(xmlString, 'text/xml');
  } catch (e) {
    console.log(e, '出现异常');
  }
  return xmlDoc;
}
