<!--
 * @Author: AI Assistant
 * @Date: 2024-12-19
 * @Description: 工具调用显示组件
-->
<template>
  <div class="tool-container">
    <div class="tool-title" @click="toggleCollapse">
      <span>🔧 {{ toolName }}</span>
      <span :class="['tool-toggle', {collapsed: isCollapsed}]">▼</span>
    </div>

    <div :class="['tool-content', {collapsed: isCollapsed}]" :style="{maxHeight: contentHeight}">
      <!-- 工具参数 -->
      <div v-if="parameters" class="tool-section">
        <h4>参数:</h4>
        <div class="json-block">
          <div class="json-header" @click="toggleParams">
            <span>Parameters</span>
            <button class="json-copy-btn" @click.stop="copyJson(parameters)">复制</button>
          </div>
          <div :class="['json-content', {collapsed: paramsCollapsed}]">
            <pre><code>{{ formatJson(parameters) }}</code></pre>
          </div>
        </div>
      </div>

      <!-- 工具结果 -->
      <div v-if="result" class="tool-section">
        <h4>结果:</h4>
        <div class="json-block">
          <div class="json-header" @click="toggleResult">
            <span>Result</span>
            <button class="json-copy-btn" @click.stop="copyJson(result)">复制</button>
          </div>
          <div :class="['json-content', {collapsed: resultCollapsed}]">
            <pre><code>{{ formatJson(result) }}</code></pre>
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="tool-section error">
        <h4>错误:</h4>
        <div class="error-content">
          {{ error }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted} from 'vue';

// Props
interface Props {
  toolName: string;
  parameters?: any;
  result?: any;
  error?: string;
  collapsed?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false,
});

// 响应式数据
const isCollapsed = ref(props.collapsed);
const paramsCollapsed = ref(false);
const resultCollapsed = ref(false);

// 计算属性
const contentHeight = computed(() => {
  if (isCollapsed.value) return '0px';

  let height = 0;
  if (props.parameters) height += 200;
  if (props.result) height += 200;
  if (props.error) height += 100;

  return `${Math.max(height, 100)}px`;
});

// 方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

const toggleParams = () => {
  paramsCollapsed.value = !paramsCollapsed.value;
};

const toggleResult = () => {
  resultCollapsed.value = !resultCollapsed.value;
};

const formatJson = (obj: any): string => {
  try {
    return JSON.stringify(obj, null, 2);
  } catch (e) {
    return String(obj);
  }
};

const copyJson = async (obj: any) => {
  try {
    const text = formatJson(obj);
    await navigator.clipboard.writeText(text);
    // 这里可以添加复制成功的提示
  } catch (err) {
    console.error('复制失败:', err);
  }
};

// 组件挂载时的处理
onMounted(() => {
  // 可以在这里添加初始化逻辑
});
</script>

<style lang="less" scoped>
.tool-container {
  margin: 15px 0;
  width: 90%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tool-title {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 20px;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  user-select: none;
  transition: background 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  }
}

.tool-toggle {
  transition: transform 0.3s ease;
  font-size: 12px;

  &.collapsed {
    transform: rotate(-90deg);
  }
}

.tool-content {
  background: white;
  overflow: hidden;
  transition: max-height 0.3s ease-out;

  &.collapsed {
    max-height: 0 !important;
  }
}

.tool-section {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);

  &:last-child {
    border-bottom: none;
  }

  &.error {
    background: rgba(231, 76, 60, 0.05);
  }

  h4 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    font-size: 1.1em;
    font-weight: 600;
  }
}

.json-block {
  background: #1e1e1e;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.json-header {
  background: #2d2d2d;
  color: #e0e0e0;
  padding: 12px 16px;
  font-family: 'Consolas', monospace;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;

  &:hover {
    background: #363636;
  }
}

.json-content {
  margin: 0;
  padding: 16px;
  background: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre;
  transition: all 0.3s ease-out;
  border-top: 1px solid #363636;

  &.collapsed {
    max-height: 0 !important;
    padding-top: 0;
    padding-bottom: 0;
    border-top: none;
  }

  pre {
    margin: 0;
    padding: 0;
    background: transparent;

    code {
      background: transparent;
      color: inherit;
      padding: 0;
      border: none;
    }
  }
}

.json-copy-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.75em;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }
}

.error-content {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 4px solid #e74c3c;
  font-family: monospace;
}

// JSON 语法高亮
:deep(.json-content) {
  .string {
    color: #ce9178;
  }

  .number {
    color: #b5cea8;
  }

  .boolean {
    color: #569cd6;
  }

  .null {
    color: #569cd6;
  }

  .key {
    color: #9cdcfe;
  }
}
</style>
