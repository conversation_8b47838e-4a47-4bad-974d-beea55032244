/*
 * @Author: 老范
 * @Date: 2023-02-24 09:53:51
 * @LastEditors: 老范
 * @LastEditTime: 2024-03-07 09:53:15
 * @Description: 请填写简介
 */
import {createApp} from 'vue';
import '@/style.css';
import App from '@/App.vue';
import router from '@/router/index';
import {store} from '@/pinia/index';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import 'element-plus/dist/index.css';
import '@/assets/iconfont/iconfont.css';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import CKEditorPlugin from '@ckeditor/ckeditor5-vue';

const app = createApp(App);
// 挂载element-icon
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.use(router);
app.use(store);
app.use(ElementPlus, {locale: zhCn});
app.use(CKEditorPlugin);

app.mount('#app');
