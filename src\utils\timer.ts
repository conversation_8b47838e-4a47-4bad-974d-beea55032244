/*
 * @Author: 老范
 * @Date: 2023-06-29 15:40:15
 * @LastEditors: 老范
 * @LastEditTime: 2023-06-29 15:57:02
 * @Description: 时间处理模块
 */
import moment from 'moment';

interface timerType {
  timestamp2Time: Function;
  date2Ms: Function;
  dateFormatting: Function;
}
const timer: timerType = {
  // 时间戳/时间字符串转标准中国时间
  timestamp2Time(value: string | number | Date): Date {
    return new Date(value);
  },
  // 中国标准时间转时间戳
  date2Ms(value: string): number {
    return new Date(value).getTime();
  },
  // 时间格式化
  dateFormatting(value: moment.MomentInput): string {
    return moment(value).format('YYYY-MM-DD HH:mm:ss');
  },
};
export default timer;
