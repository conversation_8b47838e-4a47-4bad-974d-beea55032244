<!--
 * <AUTHOR> laofan
 * @Date         : 2023-02-24 09:53:51
 * @LastEditors: 老范
 * @LastEditTime: 2023-08-30 11:12:35
 * @Description  : 请填写简介
-->
<script setup lang="ts">
import ChatAssistant from '@/views/ChatAssistant.vue';
</script>

<template>
  <div class="contentBox">
    <ChatAssistant></ChatAssistant>
  </div>
</template>

<style lang="less" scoped>
.contentBox {
  width: 100vw;
  height: 100vh;
  // background-image: url('/images/boxBorder.png');
  background-size: 100% 100%;
}
</style>
