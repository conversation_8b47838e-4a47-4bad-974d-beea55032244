import axios, {AxiosInstance, AxiosRequestConfig} from 'axios'; // 引入axios
import {ElMessage} from 'element-plus';
import type {MyResponseType} from '@/types/index';
interface serviceConfigType extends AxiosInstance {
  urlType: string;
}
const {baseURL, controlURL} = configUrl;
const service: any = axios.create({
  baseURL,
  timeout: 99999,
});

// http request 拦截器
service.interceptors.request.use(
  (config: AxiosRequestConfig | any) => {
    config.headers = {
      'Content-Type': 'application/json',
      // 'x-token': userStore.token,
      ...config.headers,
    };
    switch (config.urlType) {
      // 如果项目需拓展多个后端地址,添加case,并在public/config/config.js中添加配置项
      case 'api':
        config.url = controlURL + config.url;
        break;
      // 默认后端环境
      default:
        config.url = baseURL + config.url;
        break;
    }
    return config;
  },
  (error: any) => {
    ElMessage({
      showClose: true,
      message: error,
      type: 'error',
    });
    return error;
  }
);
const successCodeAry: number[] = [200, 201, 202, 204];
const errorCodeAry: number[] = [400, 401, 403, 404, 406, 410, 422, 500];
// http response 拦截器
service.interceptors.response.use(
  (response: MyResponseType) => {
    console.log('🚀 ~ file: request.ts:57 ~ response:', response);
    if (successCodeAry.includes(response.status))
      ElMessage({
        showClose: true,
        message: response.data.msg,
        type: 'success',
      });
    return response.data;
  },
  (error: any) => {
    console.log(error, 'error');
    if (error) {
      ElMessage({
        showClose: true,
        message: error,
        type: 'error',
      });
      return;
    }
    if (errorCodeAry.includes(error.response.status))
      ElMessage({
        showClose: true,
        message: error.response.data,
        type: 'error',
      });

    // ElMessage({
    //   showClose: true,
    //   message: error,
    //   type: 'error',
    // });
    sessionStorage.clear();
    return error.data;
  }
);
export default service;
