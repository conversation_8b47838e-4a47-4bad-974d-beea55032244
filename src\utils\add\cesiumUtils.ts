/*
 * @Author: liukun
 * @Date: 2023-08-11 09:30:48
 * @LastEditors: liukun
 * @LastEditTime: 2023-08-11 10:12:40
 * @FilePath: \vue3_template\src\utils\add\cesiumUtils.ts
 * @Description:
 *
 */
import {Cesium} from 'mars3d';
type position = {
  lon: number;
  lat: number;
  alt: number;
};

/**
 * @description: 根据水平角的度数和张角的范围计算出雷达扫描的开始角度和结束角度
 * @param {number} centerAngle 水平角角度
 * @param {number} spanAngle 张角角度
 * @return {*} 雷达扫描开始角度和结束角度
 */
export const flareAngle = (centerAngle: number, spanAngle: number): {start: number; end: number} => {
  const start = centerAngle + (centerAngle >= 0 ? -(spanAngle / 2) : spanAngle / 2);
  const end = centerAngle + (centerAngle >= 0 ? spanAngle / 2 : -(spanAngle / 2));
  return {start, end};
};
/**
 * @description: 计算两个点位之间的三维距离(连带高度计算)
 * @param {position} pos1 点位1
 * @param {position} pos2 点位2
 * @return {*} 两地之间的距离。 单位：米
 */
export const calcDistance = (pos1: position, pos2: position): number => {
  const p1 = Cesium.Cartesian3.fromDegrees(pos1.lon, pos1.lat, pos1.lat);
  const p2 = Cesium.Cartesian3.fromDegrees(pos2.lon, pos2.lat, pos2.lat);
  const threeDdistance = Cesium.Cartesian3.distance(p1, p2); // 计算三维距离
  return threeDdistance;
};

/**
 * @description: 已知要探测的目标位置和雷达最远探测距离, 计算雷达最终探测的位置
 * @param {position} pos1 雷达自身的位置
 * @param {position} pos2 已知要探测的目标位置
 * @param {number} maxDistance 雷达能够探测的最大距离
 * @return {*} 雷达最终探测的位置(笛卡尔坐标)
 */
export const calculateTatgetPos = (pos1: position, pos2: position, maxDistance: number): Cesium.Cartesian3 => {
  const threeDdistance = calcDistance(pos1, pos2); // 计算三维距离
  if (threeDdistance < maxDistance) return Cesium.Cartesian3.fromDegrees(pos2.lon, pos2.lat, pos2.alt);
  const newDistance = maxDistance / threeDdistance;
  const latC = pos1.lat + newDistance * (pos2.lat - pos1.lat); // 新点的纬度
  const lonC = pos1.lon + newDistance * (pos2.lon - pos1.lon); // 新点的经度
  const altC = pos1.alt + newDistance * (pos2.alt - pos1.alt); // 新点的高度
  return Cesium.Cartesian3.fromDegrees(lonC, latC, altC);
};
