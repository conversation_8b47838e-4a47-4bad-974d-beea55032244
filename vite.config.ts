/*
 * <AUTHOR> laofan
 * @Date         : 2023-02-24 09:53:51
 * @LastEditors: 老范
 * @LastEditTime: 2024-03-07 09:52:52
 * @Description  : 请填写简介
 */
import {defineConfig} from 'vite';
import vue from '@vitejs/plugin-vue';
import cesium from 'vite-plugin-cesium';
import path from 'path';

import ckeditor5 from '@ckeditor/vite-plugin-ckeditor5';
export default defineConfig({
  plugins: [vue(), cesium(), ckeditor5({theme: require.resolve('@ckeditor/ckeditor5-theme-lark')})],
  server: {
    host: '0.0.0.0',
    port: 8686, //这里自行更改
    open: false, //是否打开浏览器
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  optimizeDeps: {
    exclude: ['@wiris/mathtype-html-integration-devkit'],
  },
  build: {
    sourcemap: true,
  },
});
