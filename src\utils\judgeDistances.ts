/*
 * @Author: 老范
 * @Date: 2023-08-29 11:14:05
 * @LastEditors: 老范
 * @LastEditTime: 2023-08-29 11:19:47
 * @Description: 判断地图上两点间的距离
 */
import type {featureType} from '@/types/index';

/**
 * @description:计算点到圆心的距离
 * @param {center_data 圆心}
 * @param {Target 目标点}
 * @return {*}
 */
const pi = 3.1415926;

export function judgePosition(center_data: [number, number] | any, Target: [number, number]): number {
  const x = (center_data[0] / 180) * pi;
  const y = (center_data[1] / 180) * pi;
  const posLon = (Target[0] / 180) * pi;
  const posLat = (Target[1] / 180) * pi;
  const FF = 1 / 298.25722293287;
  const RE = 6378137;
  const Rdenominator = Math.pow(1.0 - FF, 2.0) * Math.pow(Math.sin(y), 2.0) + Math.pow(Math.cos(y), 2.0);
  const R = RE / Math.sqrt(Rdenominator);
  const deltaLon = Math.sin(posLon - x) * R; // 经度差
  const deltaLat = Math.sin(posLat - y) * R; // 纬度差
  // 距离/m
  const distance = Math.sqrt(Math.pow(deltaLon, 2.0) + Math.pow(deltaLat, 2.0));
  return distance;
}
/**
 * @description:遍历并找出属于哪个实体
 * @param {jsonAry geojson源}
 * @param {Target 目标点}
 * @return {*}
 */
// export function traverseJson(jsonAry: any[], Target: [number, number]): string {
//   let num = 1000; //zd覆盖半径
//   let result: string | undefined | number = '';
//   jsonAry.forEach((item: featureType) => {
//     if (item.properties.type == 'ZD') {
//       const distance = judgePosition(item.geometry.coordinates, Target);
//       if (distance < num) {
//         num = distance;
//         result = item.properties.entityId;
//       }
//     }
//   });
//   console.log(num, num);
//   return result;
// }
judgePosition([0, 0], [1, 1]);
