/*
 * <AUTHOR> 老范
 * @Date         : 2022-08-09 09:30:45
 * @LastEditors: 老范
 * @LastEditTime: 2025-06-18 16:53:24
 * @Description  : 请填写简介
 */
import { createRouter, createWebHistory } from "vue-router";

const routes = [
  {
    path: "/",
    name: "",
    redirect: "/Index",
  },
  {
    path: "/ChatAssistant",
    name: "ChatAssistant",
    component: () => import("@/views/ChatAssistant.vue"),
  },
];
const router = createRouter({
  history: createWebHistory(),
  routes,
});
export default router;
