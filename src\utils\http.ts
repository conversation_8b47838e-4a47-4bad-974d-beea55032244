/*
 * @Author: 老范
 * @Date: 2023-05-09 11:06:40
 * @LastEditors: 老范
 * @LastEditTime: 2023-05-09 15:17:32
 * @Description: 请填写简介
 */
import service from '@/utils/request';
interface methodConfigType {
  method: string;
  url: string;
  data?: object;
  params?: object;
  headers?: object;
}
const http = {
  /**
   * methods: 请求
   * @param url 请求地址
   * @param params 请求参数
   */
  get(url: string, params?: object) {
    const config: methodConfigType = {
      method: 'get',
      url: url,
    };
    if (params) config.params = params;
    return service(config);
  },
  post(url: string, params?: object, headers?: object) {
    const config: methodConfigType = {
      method: 'post',
      url: url,
    };
    if (params) config.data = params;
    if (headers) config.headers = headers;
    return service(config);
  },
  put(url: string, params?: object) {
    const config: methodConfigType = {
      method: 'put',
      url: url,
    };
    if (params) config.data = params;
    return service(config);
  },
  delete(url: string, params?: object) {
    const config: methodConfigType = {
      method: 'delete',
      url: url,
    };
    if (params) config.data = params;
    return service(config);
  },
};
//导出
export default http;
