/*
 * @Author: 老范
 * @Date: 2023-08-28 15:05:00
 * @LastEditors: 老范
 * @LastEditTime: 2023-08-29 10:49:03
 * @Description: 数组排序 字母、数字、汉字
 */

export const sortResult = (data: string[], type: string) => {
  let result = [];
  switch (type) {
    // 当比较的item为对象的某个属性时，value = item [key]

    // 1.中文(按第一个文字)
    case 'chinese':
      result = data.sort((item1, item2) => {
        const value1 = item1.charAt(0);
        const value2 = item2.charAt(0);
        // 这里localeCompare应该是不支持第二个参数的 但是并没有报错，请直接使用value1.localeCompare(value2)
        return value1.localeCompare(value2, 'zh-CN');
      });
      break;
    // 2.字母(按第一个字母且不区分大小写，请自行修改)
    case 'letter':
      result = data.sort((item1, item2) => {
        //2.1不区分大小写
        const value1 = item1.charAt(0).toLowerCase();
        const value2 = item2.charAt(0).toLowerCase();
        return value1.localeCompare(value2);
        // //2.2区分大小写
        // const value1 = item1.charAt(0);
        // const value2 = item2.charAt(0);
        // //测试2.2
        // const arr = ['A', 'a', 'b', 'B'];
        // arr.sort((item1, item2) => {
        //   return item1.localeCompare(item2);
        // });
        //输出["a", "A", "b", "B"]
      });
      break;
    //3.数字
    case 'number':
      result = data.sort((item1, item2) => {
        const value1 = item1;
        const value2 = item2;
        return Number(value1) - Number(value2);
      });
      break;

    default:
      result = data;
      break;
  }
  return result;
};
