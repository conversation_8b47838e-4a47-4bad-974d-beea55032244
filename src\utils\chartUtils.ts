/*
 * @Author: 老范
 * @Date: 2023-07-24 17:35:46
 * @LastEditors: 老范
 * @LastEditTime: 2023-08-11 13:24:53
 * @Description: 请填写简介
 */

import {layoutType, traceType} from '@/types/chartType';

/**
 * @description: 改变盒子大小(全屏/半屏切换)
 * @param {string} judgeRefName 要改变的盒子dom
 * @param {number} layoutWidth 当前盒子最大宽度
 * @param {traceType} leftSource 左侧图表数据源
 * @param {traceType} rightSource 右侧图表数据源
 * @param {any} leftLayout 左侧图表layout
 * @param {any} rightLayout 右侧图表layout
 * @param {boolean} isFullScreen 当前是否全屏
 * @param {*} leftDom 左侧盒子dom
 * @param {*} rightDom 右侧盒子dom
 * @param {Function} callBack 回调函数 改变是否全屏的状态
 * @return {*}
 */
interface HTMLElementPlus extends HTMLElement {
  position: String;
}
interface paramObjType {
  layoutWidth: number;
  leftSource: traceType[];
  rightSource: traceType[];
  leftLayout: layoutType;
  rightLayout: layoutType;
  isFullScreen: boolean;
  leftDom: HTMLElementPlus;
  rightDom: HTMLElementPlus;
}

export const changeScreen = (judgeRefName: string, paramObj: paramObjType, callBack: Function): void => {
  const {layoutWidth, leftSource, rightSource, leftLayout, rightLayout, isFullScreen, leftDom, rightDom} = paramObj;
  const dataSource = judgeRefName === 'left' ? leftSource : rightSource;
  const layout = judgeRefName === 'left' ? leftLayout : rightLayout;
  if (isFullScreen === false) {
    // layout.width = layoutWidth;
    document.documentElement.style.setProperty('--position', ' ');
    judgeRefName === 'left' ? (leftDom.position = 'absolute') : (rightDom.position = 'absolute');
    judgeRefName === 'left' ? leftDom.classList.add('fullStyle') : rightDom.classList.add('fullStyle');
  } else {
    // layout.width = layoutWidth / 2;
    document.documentElement.style.setProperty('--position', 'relative');
    judgeRefName === 'left' ? leftDom.classList.remove('fullStyle') : rightDom.classList.remove('fullStyle');
  }
  Plotly.react(judgeRefName === 'left' ? leftDom : rightDom, dataSource, layout, {
    displayModeBar: true,
    displaylogo: false,
    responsive: true,
  });
  callBack();
};
