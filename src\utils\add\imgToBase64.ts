/*
 * @Author: 老范
 * @Date: 2023-08-28 13:14:05
 * @LastEditors: 老范
 * @LastEditTime: 2023-08-28 13:28:40
 * @Description: 请填写简介
 */
/**
 *
 * @description 图片文件转换为base64
 * @param file 图片导入文件
 * @returns 图片转换的base64
 */
// 图片转base64
export default function getBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    let imgResult: any = '';
    reader.readAsDataURL(file);
    reader.onload = function () {
      imgResult = reader.result;
    };
    reader.onerror = function (error) {
      reject(error);
    };
    reader.onloadend = function () {
      resolve(imgResult);
    };
  });
}
