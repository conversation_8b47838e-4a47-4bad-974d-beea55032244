/*
 * <AUTHOR> 老范
 * @Date         : 2022-08-12 13:38:01
 * @LastEditors  : 老范
 * @LastEditTime : 2022-08-31 15:10:34
 * @Description  : 公用types
 */

export interface MyResponseType<T = any> {
  code?: number;
  message?: string;
  data: T;
  status: number;
}
export interface GeoJsonType {
  type: string;
  data: GeoJsonDataType;
}
export interface GeoJsonDataType {
  type: string;
  features: featureType[];
}
export interface featureType {
  type: string;
  geometry: geometryType;
  id?: string;
  properties: propertiesType;
}
export interface propertiesType {
  entityIcon?: string;
  entityId?: number | string;
  name?: string;
  latitude?: number;
  longitude?: number;
  modelName?: string;
  type?: string;
  parameters?: any;
  angle?: number; // 张角
  yaw?: number; //俯仰角
  range?: number; //覆盖范围
  deploy?: number[] | string[]; // 部署的其他实体
  belong?: number | string; //所附属的实体
  speed?: number; //限速信息
}

interface geometryType {
  coordinates: Array<number>[] | number[];
  type: string;
}
