<!--
 * @Author: AI Assistant
 * @Date: 2024-12-19
 * @Description: MCP 智能助手聊天界面
-->
<template>
  <div class="chat-container">
    <!-- <div class="chat-header">运控导调智能助手</div> -->
    <!-- <div :class="['connection-status', {disconnected: !isConnected}]">
      {{ connectionStatus }}
    </div> -->
    <div ref="chatMessagesRef" class="chat-messages">
      <!-- 消息列表 -->
      <div v-for="message in messages" :key="message.id" :class="['message', `${message.type}-message`]">
        <!-- 普通消息内容 -->
        <div v-if="message.type !== 'tool'" class="message-content" v-html="renderMessage(message)"></div>

        <!-- 工具调用显示 -->
        <ToolCallDisplay v-else :tool-name="message.metadata?.toolName || '未知工具'" :parameters="message.metadata?.parameters" :result="message.metadata?.result" :error="message.metadata?.error" />
      </div>

      <!-- 思考指示器 -->
      <div v-if="isThinking" class="typing-indicator show">
        {{ thinkingText }}
      </div>
    </div>

    <div class="chat-input">
      <div class="input-container" style="position: relative">
        <textarea ref="inputRef" v-model="userInput" class="input-field" placeholder="请输入您的问题..." rows="3" @keydown="handleKeydown" @input="adjustTextareaHeight"></textarea>
        <button :disabled="!canSend && !canStop" class="send-button inside-input" @click="handleButtonClick">{{ buttonText }}</button>
        <!-- <button class="voice-button inside-input" :class="{recording: isRecording}" :title="isRecording ? '点击停止语音识别' : '语音输入'" style="right: 70px" @click="toggleVoiceRecognition">
          <span v-if="!isRecording">🎤</span>
          <span v-else>■</span>
        </button> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, nextTick, computed} from 'vue';
import {useChatStore} from '@/pinia/chat';
import {marked} from 'marked';
import hljs from 'highlight.js';
import 'highlight.js/styles/github-dark.css';
import ToolCallDisplay from '@/components/ToolCallDisplay.vue';

// 使用 Pinia store
const chatStore = useChatStore();

// 响应式数据
const userInput = ref('');
const inputRef = ref<HTMLTextAreaElement>();
const chatMessagesRef = ref<HTMLElement>();
const isRecording = ref(false);
let recognition: any = null;

// 计算属性
const messages = computed(() => chatStore.messages);
const isConnected = computed(() => chatStore.isConnected);
const isProcessing = computed(() => chatStore.isProcessing);
const isThinking = computed(() => chatStore.isThinking);
const thinkingText = computed(() => chatStore.thinkingText);
const connectionStatus = computed(() => (isConnected.value ? '已连接' : '连接断开'));

// 新增：按钮状态和文本
const canSend = computed(() => userInput.value.trim().length > 0 && !isProcessing.value);
const canStop = computed(() => isProcessing.value);
const buttonText = computed(() => {
  if (isProcessing.value) return '停止';
  return '发送';
});

// 配置 marked
marked.setOptions({
  gfm: true,
  breaks: true,
  highlight: (code: string, lang: string) => {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, {language: lang}).value;
      } catch (e) {
        console.error('Code highlight error:', e);
        return code;
      }
    }
    return code;
  },
});

// 渲染消息内容
const renderMessage = (message: any) => {
  if (message.type === 'user') {
    return message.content;
  } else if (message.type === 'assistant') {
    const html = marked.parse(message.content);
    // 为代码块添加复制按钮
    return html.replace(/<pre><code([^>]*)>/g, '<pre><code$1><button class="copy-btn" onclick="copyCode(this)">复制</button>');
  } else if (message.type === 'thinking') {
    return `🤔 ${message.content}`;
  }
  return message.content;
};

// 复制代码功能
const copyCode = (button: HTMLElement) => {
  const pre = button.closest('pre');
  if (pre) {
    const code = pre.querySelector('code');
    if (code) {
      const text = code.textContent || '';
      navigator.clipboard
        .writeText(text)
        .then(() => {
          button.textContent = '已复制';
          setTimeout(() => {
            button.textContent = '复制';
          }, 2000);
        })
        .catch(err => {
          console.error('复制失败:', err);
          button.textContent = '复制失败';
          setTimeout(() => {
            button.textContent = '复制';
          }, 2000);
        });
    }
  }
};

// 将复制函数暴露到全局，供模板中的 onclick 使用
(window as any).copyCode = copyCode;

// 调整文本框高度
const adjustTextareaHeight = () => {
  if (inputRef.value) {
    inputRef.value.style.height = 'auto';
    const scrollHeight = inputRef.value.scrollHeight;
    const maxHeight = 150;
    inputRef.value.style.height = Math.min(scrollHeight, maxHeight) + 'px';
  }
};

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};

// 新增：按钮点击事件
const handleButtonClick = () => {
  if (isProcessing.value) {
    chatStore.stopThinking();
  } else {
    sendMessage();
  }
};

// 发送消息
const sendMessage = async () => {
  const message = userInput.value.trim();
  if (!message || isProcessing.value) return;

  // 添加用户消息
  chatStore.addMessage({
    id: Date.now().toString(),
    type: 'user',
    content: message,
    timestamp: new Date(),
  });

  // 清空输入框
  userInput.value = '';
  adjustTextareaHeight();

  // 滚动到底部
  await nextTick();
  scrollToBottom();

  // 发送到后端
  await chatStore.sendMessage(message);
};

// 滚动到底部
const scrollToBottom = () => {
  if (chatMessagesRef.value) {
    chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight;
  }
};

// 语音识别相关
const toggleVoiceRecognition = () => {
  if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
    alert('当前浏览器不支持语音识别');
    return;
  }
  if (!recognition) {
    const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
    recognition = new SpeechRecognition();
    recognition.lang = 'zh-CN';
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.onresult = (event: any) => {
      console.log('🚀 ~ toggleVoiceRecognition ~ event:', event);
      if (event.results && event.results[0] && event.results[0][0]) {
        userInput.value += event.results[0][0].transcript;
      }
      isRecording.value = false;
    };
    recognition.onerror = (event: any) => {
      console.error('语音识别错误:', event.error, event.message);
      let errorMessage = `语音识别失败: ${event.error}`;
      if (event.error === 'not-allowed') {
        errorMessage = '您需要授权麦克风权限才能使用语音识别功能。请检查浏览器设置。';
      } else if (event.error === 'no-speech') {
        errorMessage = '没有检测到语音，请再说一次。';
      } else if (event.error === 'network') {
        errorMessage = '网络错误，请检查您的网络连接。';
      }
      alert(errorMessage);
      isRecording.value = false;
    };
    recognition.onend = () => {
      isRecording.value = false;
    };
  }
  if (isRecording.value) {
    recognition.stop();
    isRecording.value = false;
  } else {
    recognition.start();
    isRecording.value = true;
  }
};

// 组件挂载
onMounted(() => {
  // 初始化连接
  chatStore.initConnection();

  // 滚动到底部
  scrollToBottom();
});
</script>

<style lang="less" scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.chat-container {
  width: 100%;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 30px;
  text-align: center;
  font-size: 1.8em;
  font-weight: 700;
  letter-spacing: 1px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  position: relative;
  z-index: 10;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 2px;
  }
}

.connection-status {
  padding: 12px 20px;
  text-align: center;
  font-size: 0.9em;
  font-weight: 500;
  background: linear-gradient(90deg, #d4edda, #c3e6cb);
  color: #155724;
  border-bottom: 1px solid rgba(195, 230, 203, 0.6);
  transition: all 0.3s ease;
  position: relative;
  z-index: 9;

  &.disconnected {
    background: linear-gradient(90deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border-bottom-color: rgba(245, 198, 203, 0.6);
  }
}

.chat-messages {
  flex: 1;
  padding: 25px;
  overflow-y: auto;
  background: transparent;
  position: relative;
  z-index: 1;
}

.message {
  margin-bottom: 20px;
  padding: 16px 20px;
  border-radius: 18px;
  max-width: 75%;
  word-wrap: break-word;
  line-height: 1.6;
  position: relative;
  animation: messageSlideIn 0.4s ease-out;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-message {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-left: auto;
  text-align: right;
  border-bottom-right-radius: 6px;

  &::before {
    content: '';
    position: absolute;
    top: 10px;
    right: -10px;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    border-left-color: #764ba2;
  }
}

.assistant-message {
  background: rgba(255, 255, 255, 0.95);
  color: #2c3e50;
  margin-right: auto;
  border-bottom-left-radius: 6px;
  border: 1px solid rgba(102, 126, 234, 0.1);

  &::before {
    content: '';
    position: absolute;
    top: 10px;
    left: -10px;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    border-right-color: white;
  }
}

.thinking-message {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  margin-right: auto;
  border-bottom-left-radius: 6px;
  border: 2px solid rgba(102, 126, 234, 0.2);
}

.chat-input {
  display: flex;
  padding: 25px 30px 30px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(102, 126, 234, 0.2);
  align-items: flex-end;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.input-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 15px;
}

.input-field {
  min-height: 50px;
  max-height: 150px;
  padding: 15px 20px;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  font-size: 16px;
  font-family: inherit;
  outline: none;
  resize: none;
  transition: all 0.3s ease;
  line-height: 1.5;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding-right: 90px;

  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
  }
}

.input-hint {
  font-size: 0.85em;
  color: #6c757d;
  margin-top: 8px;
  opacity: 0.8;
  text-align: center;
}

.send-button.inside-input {
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  height: 36px;
  padding: 0 18px;
  border-radius: 18px;
  font-size: 15px;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
  transition: background 0.2s;
}

.voice-button.inside-input {
  position: absolute;
  right: 70px;
  top: 50%;
  transform: translateY(-50%);
  height: 36px;
  width: 36px;
  border-radius: 50%;
  background: #f3f4f6;
  border: none;
  color: #667eea;
  font-size: 20px;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.08);
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-button.inside-input.recording {
  background: #ffebee;
  color: #e53935;
}

.typing-indicator {
  display: none;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 12px 20px;
  border-radius: 25px;
  margin: 15px auto;
  max-width: 700px;
  text-align: center;
  font-style: italic;
  font-weight: 500;
  animation: typingPulse 2s ease-in-out infinite;
  border: 2px solid rgba(102, 126, 234, 0.2);

  &.show {
    display: block;
  }
}

@keyframes typingPulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

// Markdown 样式增强
.assistant-message {
  :deep(h1),
  :deep(h2),
  :deep(h3) {
    margin: 20px 0 12px 0;
    color: #2c3e50;
    font-weight: 600;
    position: relative;
  }

  :deep(h1) {
    font-size: 1.6em;
    border-bottom: 3px solid #667eea;
    padding-bottom: 10px;
  }

  :deep(h2) {
    font-size: 1.4em;
    border-bottom: 2px solid #764ba2;
    padding-bottom: 8px;
  }

  :deep(h3) {
    font-size: 1.2em;
    color: #667eea;
  }

  :deep(p) {
    margin: 10px 0;
    line-height: 1.7;
    text-align: justify;
  }

  :deep(ul),
  :deep(ol) {
    margin: 12px 0;
    padding-left: 25px;
  }

  :deep(li) {
    margin: 6px 0;
    position: relative;
  }

  :deep(ul li::marker) {
    color: #667eea;
  }

  :deep(blockquote) {
    border-left: 5px solid #667eea;
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.1), transparent);
    margin: 15px 0;
    padding: 12px 20px;
    font-style: italic;
    border-radius: 0 8px 8px 0;
  }

  :deep(code) {
    background: linear-gradient(135deg, #f1f2f6, #e9ecef);
    padding: 4px 8px;
    border-radius: 6px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
    font-size: 0.9em;
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.2);
  }

  :deep(pre) {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: #ecf0f1;
    padding: 20px;
    border-radius: 12px;
    overflow-x: auto;
    margin: 15px 0;
    position: relative;
    border: 1px solid #34495e;
    box-shadow: 0 4px 15px rgba(44, 62, 80, 0.3);

    code {
      background: none;
      color: inherit;
      padding: 0;
      border: none;
      font-size: 0.9em;
      line-height: 1.5;
    }
  }

  :deep(strong) {
    font-weight: 700;
    color: #2c3e50;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), transparent);
    padding: 2px 4px;
    border-radius: 4px;
  }

  :deep(em) {
    font-style: italic;
    color: #7f8c8d;
    background: rgba(127, 140, 141, 0.1);
    padding: 1px 3px;
    border-radius: 3px;
  }

  :deep(a) {
    color: #667eea;
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: border-bottom-color 0.3s ease;

    &:hover {
      border-bottom-color: #667eea;
    }
  }

  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    th,
    td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #e9ecef;
    }

    th {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      font-weight: 600;
    }

    tr:hover {
      background: rgba(102, 126, 234, 0.05);
    }
  }
}

// 代码高亮样式
:deep(.hljs) {
  background: #1e1e1e !important;
  color: #d4d4d4 !important;
}

:deep(.hljs-string) {
  color: #ce9178 !important;
}

:deep(.hljs-number) {
  color: #b5cea8 !important;
}

:deep(.hljs-literal) {
  color: #569cd6 !important;
}

:deep(.hljs-attr) {
  color: #9cdcfe !important;
}

:deep(.hljs-keyword) {
  color: #569cd6 !important;
}

:deep(.hljs-comment) {
  color: #6a9955 !important;
}

:deep(.hljs-function) {
  color: #dcdcaa !important;
}

:deep(.hljs-variable) {
  color: #9cdcfe !important;
}

// 复制按钮样式
.copy-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(102, 126, 234, 0.8);
  border: none;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.75em;
  font-weight: 500;
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(102, 126, 234, 1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }
}

.assistant-message :deep(pre:hover .copy-btn) {
  opacity: 1;
  transform: translateY(-2px);
}

// 响应式设计
@media (max-width: 768px) {
  .chat-container {
    height: 100vh;
  }

  .chat-header {
    padding: 15px 20px;
    font-size: 1.5em;
  }

  .chat-messages {
    padding: 15px;
  }

  .message {
    max-width: 90%;
    padding: 12px 16px;
  }

  .chat-input {
    padding: 20px 15px 25px;
  }

  .input-field {
    font-size: 14px;
    padding: 12px 16px;
  }

  .send-button.inside-input {
    padding: 0 15px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .chat-header {
    font-size: 1.3em;
    padding: 12px 15px;
  }

  .message {
    max-width: 95%;
    padding: 10px 14px;
  }

  .chat-input {
    padding: 15px 10px 20px;
  }

  .input-container {
    margin-right: 10px;
  }

  .send-button.inside-input {
    padding: 0 12px;
    font-size: 13px;
  }
}
</style>
