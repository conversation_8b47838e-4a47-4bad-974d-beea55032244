<!--
 * @Author: 老范
 * @Date: 2023-05-10 16:34:57
 * @LastEditors: 老范
 * @LastEditTime: 2023-10-31 15:22:52
 * @Description: 请填写简介
-->

<script setup lang="ts">
import {ref} from 'vue';
import page1 from '@/views/PageOne.vue';
import page2 from '@/views/PageTwo.vue';
defineProps<{msg: string}>();

const count = ref(0);
</script>

<template>
  <page1></page1>
  <page2></page2>
  <div>{{ msg }}</div>
  <div>{{ count }}</div>
</template>

<style scoped>
.read-the-docs {
  color: #888;
}
</style>
